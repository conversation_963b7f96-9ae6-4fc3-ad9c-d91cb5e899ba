// lib/screens/homepage.dart
import 'dart:async'; // Import for Timer (debouncer)
import 'package:flutter/material.dart';
import 'package:intl/intl.dart'; // Import for date formatting
import 'package:tut/models/log.dart';
import 'package:tut/services/logservice.dart';

import '../components/log_card.dart';

enum School { International, High, Primary }

class Homepage extends StatefulWidget {
  const Homepage({super.key});

  @override
  State<Homepage> createState() => _HomepageState();
}

class _HomepageState extends State<Homepage> {
  // Core state
  bool _isLoading = true;
  final LogService _logService = LogService();

  // Data lists: one for all logs from API, one for the displayed (filtered) logs
  List<Logs?> _allLogs = [];
  List<Logs?> _filteredLogs = [];

  // Filter state
  DateTime _selectedDate = DateTime.now();
  School _selectedSchool = School.International;

  // Search state
  final TextEditingController _searchController = TextEditingController();
  Timer? _debounce;
  bool _isSearching = false;

  @override
  void initState() {
    super.initState();
    _fetchData();
    _searchController.addListener(_onSearchChanged);
  }

  @override
  void dispose() {
    _searchController.removeListener(_onSearchChanged);
    _searchController.dispose();
    _debounce?.cancel();
    super.dispose();
  }

  Future<void> _fetchData() async {
    if (mounted) setState(() => _isLoading = true);

    try {
      final fetchedLogs = await _logService.getLogs(
        school: _selectedSchool.name,
        date: _selectedDate,
      );

      // SORTING: Sort logs by date descending (newest first)
      fetchedLogs.sort((a, b) {
        // Assuming date format is like "2024-05-15T10:30:00"
        try {
          return DateTime.parse(b!.date).compareTo(DateTime.parse(a!.date));
        } catch (e) {
          // Fallback if parsing fails
          return 0;
        }
      });

      if (mounted) {
        setState(() {
          _allLogs = fetchedLogs;
          _filterLogs(); // Apply current search query to the new data
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: ${e.toString()}'),
            backgroundColor: Colors.redAccent,
          ),
        );
      }
    } finally {
      if (mounted) setState(() => _isLoading = false);
    }
  }

  // DEBOUNCER: Called when search text changes
  _onSearchChanged() {
    if (_debounce?.isActive ?? false) _debounce!.cancel();
    _debounce = Timer(const Duration(milliseconds: 500), () {
      _filterLogs();
    });
  }

  // FILTERING LOGIC
  void _filterLogs() {
    final query = _searchController.text.toLowerCase();
    setState(() {
      if (query.isEmpty) {
        _filteredLogs = List.from(_allLogs);
      } else {
        _filteredLogs =
            _allLogs.where((log) {
              final uidMatch = log!.uid.toLowerCase().contains(query);
              final operationMatch = log.operation.toLowerCase().contains(
                query,
              );
              final typeMatch = log.type.toLowerCase().contains(query);
              return uidMatch || operationMatch || typeMatch;
            }).toList();
      }
    });
  }

  Future<void> _pickDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime(2020),
      lastDate: DateTime(2030),
    );
    if (picked != null && picked != _selectedDate) {
      setState(() {
        _selectedDate = picked;
      });
      _fetchData();
    }
  }

  // Build the search field for the AppBar
  Widget _buildSearchField() {
    return TextField(
      controller: _searchController,
      autofocus: true,
      decoration: InputDecoration(
        hintText: 'Search by UID, operation...',
        border: InputBorder.none,
        hintStyle: TextStyle(
          color: Theme.of(context).colorScheme.onSurfaceVariant,
        ),
      ),
      style: TextStyle(
        color: Theme.of(context).colorScheme.onSurface,
        fontSize: 16,
      ),
    );
  }

  // Build the default title for the AppBar
  Widget _buildAppBarTitle() {
    return Text(DateFormat('EEE, MMM d').format(_selectedDate));
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Scaffold(
      backgroundColor: theme.colorScheme.background,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surfaceContainer,
        // DYNAMIC TITLE: Show search field or date title
        title: _isSearching ? _buildSearchField() : _buildAppBarTitle(),
        actions:
            _isSearching
                ? [
                  // Show a clear button when searching
                  IconButton(
                    icon: const Icon(Icons.close),
                    onPressed: () {
                      setState(() {
                        _isSearching = false;
                        _searchController.clear();
                      });
                    },
                  ),
                ]
                : [
                  // Show standard actions when not searching
                  IconButton(
                    icon: const Icon(Icons.search),
                    onPressed: () => setState(() => _isSearching = true),
                  ),
                  PopupMenuButton<School>(
                    onSelected: (School school) {
                      setState(() => _selectedSchool = school);
                      _fetchData();
                    },
                    icon: const Icon(Icons.school_outlined),
                    itemBuilder:
                        (context) =>
                            School.values
                                .map(
                                  (school) => PopupMenuItem<School>(
                                    value: school,
                                    child: Text(school.name),
                                  ),
                                )
                                .toList(),
                  ),
                  IconButton(
                    icon: const Icon(Icons.calendar_month_outlined),
                    onPressed: () => _pickDate(context),
                  ),
                ],
      ),
      body:
          _isLoading
              ? Center(child: CircularProgressIndicator())
              : RefreshIndicator(
                onRefresh: _fetchData,
                child:
                    _filteredLogs.isEmpty
                        ? Center(
                          child: Text(
                            _searchController.text.isNotEmpty
                                ? 'No results found'
                                : 'No logs for this day.',
                            style: theme.textTheme.bodyLarge,
                          ),
                        )
                        : ListView.builder(
                          padding: const EdgeInsets.only(
                            top: 8.0,
                            bottom: 80.0,
                          ), // Padding for FAB
                          itemCount: _filteredLogs.length,
                          itemBuilder: (context, index) {
                            final log = _filteredLogs[index];
                            return log != null 
                                ? LogCard(log: log)
                                : const SizedBox.shrink();
                          },
                        ),
              ),
    );
  }
}
