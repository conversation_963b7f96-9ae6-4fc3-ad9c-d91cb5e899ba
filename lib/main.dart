// main.dart
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:tut/pages/homepage.dart';

void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    // Define a seed color to generate the Material 3 color scheme
    final Color seedColor = Colors.deepPurple.shade400;

    return MaterialApp(
      title: 'Logs',
      debugShowCheckedModeBanner: false,
      theme: ThemeData(
        // Use Material 3 design
        useMaterial3: true,

        // Generate a dynamic and cohesive color scheme
        colorScheme: ColorScheme.fromSeed(
          seedColor: seedColor,
          brightness: Brightness.light, // Or Brightness.dark for a dark theme
        ),

        // Apply custom fonts to the entire app's text theme
        textTheme: GoogleFonts.poppinsTextTheme(Theme.of(context).textTheme),

        // Style specific components to match our aesthetic
        appBarTheme: AppBarTheme(
          backgroundColor:
              Colors.transparent, // Makes the AppBar blend with the scaffold
          elevation: 0,
          foregroundColor: Colors.black87,
          titleTextStyle: GoogleFonts.poppins(
            fontSize: 24,
            fontWeight: FontWeight.w600,
            color: Colors.black87,
          ),
        ),

        cardTheme: CardTheme(
          elevation: 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16.0),
          ),
        ),
      ),
      home: const Homepage(),
    );
  }
}
