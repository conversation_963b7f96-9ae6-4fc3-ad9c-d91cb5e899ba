import 'dart:async';
import 'package:flutter/material.dart';
import '../models/log.dart';
import '../services/logservice.dart';

enum SortOption { timeDesc, timeAsc, typeAsc, operationAsc }
enum School { International, High, Primary }

class LogController extends ChangeNotifier {
  // Core state
  bool _isLoading = true;
  bool _isRefreshing = false;
  final LogService _logService = LogService();

  // Data lists
  List<Logs> _allLogs = [];
  List<Logs> _filteredLogs = [];

  // Filter and search state
  DateTime _selectedDate = DateTime.now();
  School _selectedSchool = School.International;
  String _searchQuery = '';
  SortOption _sortOption = SortOption.timeDesc;
  Timer? _debounce;

  // Getters
  bool get isLoading => _isLoading;
  bool get isRefreshing => _isRefreshing;
  List<Logs> get filteredLogs => _filteredLogs;
  DateTime get selectedDate => _selectedDate;
  School get selectedSchool => _selectedSchool;
  String get searchQuery => _searchQuery;
  SortOption get sortOption => _sortOption;
  bool get hasLogs => _filteredLogs.isNotEmpty;

  @override
  void dispose() {
    _debounce?.cancel();
    super.dispose();
  }

  // Initialize data
  Future<void> initialize() async {
    await fetchLogs();
  }

  // Fetch logs from API
  Future<void> fetchLogs() async {
    _isLoading = true;
    notifyListeners();

    try {
      final fetchedLogs = await _logService.getLogs(
        school: _selectedSchool.name,
        date: _selectedDate,
      );

      _allLogs = fetchedLogs.whereType<Logs>().toList();
      _applySortingAndFiltering();
    } catch (e) {
      // Error handling will be done in the UI layer
      rethrow;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Refresh logs (pull-to-refresh)
  Future<void> refreshLogs() async {
    _isRefreshing = true;
    notifyListeners();

    try {
      await fetchLogs();
    } finally {
      _isRefreshing = false;
      notifyListeners();
    }
  }

  // Update selected date
  void updateSelectedDate(DateTime date) {
    if (_selectedDate != date) {
      _selectedDate = date;
      fetchLogs();
    }
  }

  // Update selected school
  void updateSelectedSchool(School school) {
    if (_selectedSchool != school) {
      _selectedSchool = school;
      fetchLogs();
    }
  }

  // Update search query with debouncing
  void updateSearchQuery(String query) {
    _searchQuery = query;
    
    if (_debounce?.isActive ?? false) _debounce!.cancel();
    _debounce = Timer(const Duration(milliseconds: 300), () {
      _applySortingAndFiltering();
    });
  }

  // Update sort option
  void updateSortOption(SortOption option) {
    if (_sortOption != option) {
      _sortOption = option;
      _applySortingAndFiltering();
    }
  }

  // Apply sorting and filtering
  void _applySortingAndFiltering() {
    List<Logs> filtered = List.from(_allLogs);

    // Apply search filter
    if (_searchQuery.isNotEmpty) {
      final query = _searchQuery.toLowerCase();
      filtered = filtered.where((log) {
        return log.uid.toLowerCase().contains(query) ||
               log.operation.toLowerCase().contains(query) ||
               log.type.toLowerCase().contains(query);
      }).toList();
    }

    // Apply sorting
    filtered.sort((a, b) {
      switch (_sortOption) {
        case SortOption.timeDesc:
          return _compareByTime(b, a);
        case SortOption.timeAsc:
          return _compareByTime(a, b);
        case SortOption.typeAsc:
          return a.type.compareTo(b.type);
        case SortOption.operationAsc:
          return a.operation.compareTo(b.operation);
      }
    });

    _filteredLogs = filtered;
    notifyListeners();
  }

  // Helper method to compare logs by time
  int _compareByTime(Logs a, Logs b) {
    try {
      final dateTimeA = _parseDateTime(a.date);
      final dateTimeB = _parseDateTime(b.date);
      return dateTimeA.compareTo(dateTimeB);
    } catch (e) {
      return 0;
    }
  }

  // Helper method to parse various date formats
  DateTime _parseDateTime(String dateString) {
    try {
      // Handle format like [6/8/2025 11:17:53 PM]
      if (dateString.startsWith('[') && dateString.endsWith(']')) {
        final cleanDate = dateString.substring(1, dateString.length - 1);
        return DateTime.parse(cleanDate.replaceAll('/', '-'));
      }
      // Handle ISO 8601 format
      return DateTime.parse(dateString);
    } catch (e) {
      // Return current time as fallback
      return DateTime.now();
    }
  }

  // Get sort option display name
  String getSortOptionName(SortOption option) {
    switch (option) {
      case SortOption.timeDesc:
        return 'Newest First';
      case SortOption.timeAsc:
        return 'Oldest First';
      case SortOption.typeAsc:
        return 'Type A-Z';
      case SortOption.operationAsc:
        return 'Operation A-Z';
    }
  }
}
