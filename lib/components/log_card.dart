import 'package:flutter/material.dart';
import 'package:intl/intl.dart'; // Make sure you have the intl package in pubspec.yaml
import 'package:tut/models/log.dart'; // Adjust path if necessary

class LogCard extends StatelessWidget {
  final Logs log;

  const LogCard({super.key, required this.log});

  // Helper function to extract and format the time from the date string.
  // It's robust and will handle potential parsing errors gracefully.
  String _formatTime(String dateString) {
    try {
      // Parse the full ISO 8601 date-time string
      final dateTime = DateTime.parse(dateString);
      // Format it to a user-friendly time (e.g., "3:45 PM" or "15:45")
      // Using jm() provides locale-specific time formatting like AM/PM
      return DateFormat.jm().format(dateTime);
    } catch (e) {
      // If the date string is malformed or can't be parsed, log an error
      // and return a fallback string to avoid crashing the UI.
      print("Error parsing date: $dateString, Error: $e");
      return '--:--';
    }
  }

  // Helper function to get a color based on the operation type
  Color _getOperationColor(String operation) {
    switch (operation.toUpperCase()) {
      case 'CREATE':
        return Colors.green.shade100;
      case 'UPDATE':
        return Colors.blue.shade100;
      case 'DELETE':
        return Colors.red.shade100;
      case 'READ':
      default:
        return Colors.grey.shade200;
    }
  }

  // Helper function to get the text color for the operation chip
  Color _getOperationTextColor(String operation) {
    switch (operation.toUpperCase()) {
      case 'CREATE':
        return Colors.green.shade900;
      case 'UPDATE':
        return Colors.blue.shade900;
      case 'DELETE':
        return Colors.red.shade900;
      case 'READ':
      default:
        return Colors.grey.shade800;
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 6.0),
      // Using a Stack allows us to layer widgets on top of each other.
      // We will layer the time on top of the main content area.
      child: Stack(
        children: [
          // This is the main content of the card.
          Padding(
            padding: const EdgeInsets.symmetric(
              horizontal: 16.0,
              vertical: 12.0,
            ),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                // Avatar
                CircleAvatar(
                  radius: 22,
                  backgroundColor: theme.colorScheme.primaryContainer,
                  child: Text(
                    log.uid.substring(0, 1).toUpperCase(),
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: theme.colorScheme.onPrimaryContainer,
                    ),
                  ),
                ),
                const SizedBox(width: 16),

                // Main Content Column
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // We give the title some right-padding to create space
                      // for the positioned time widget, preventing overlap.
                      Padding(
                        padding: const EdgeInsets.only(right: 48.0),
                        child: Text(
                          log.uid,
                          style: theme.textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                            height: 1.3,
                          ),
                          maxLines: 2, // Limit title to 2 lines
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      const SizedBox(height: 8),

                      // Operation and Type Tags (Chips)
                      Wrap(
                        spacing: 8.0,
                        runSpacing: 4.0,
                        children: [
                          Chip(
                            label: Text(log.operation),
                            backgroundColor: _getOperationColor(log.operation),
                            labelStyle: TextStyle(
                              color: _getOperationTextColor(log.operation),
                              fontWeight: FontWeight.w600,
                            ),
                            side: BorderSide.none,
                            visualDensity:
                                VisualDensity.compact, // Makes chip smaller
                            padding: const EdgeInsets.symmetric(horizontal: 8),
                          ),
                          Chip(
                            label: Text(log.type),
                            backgroundColor:
                                theme.colorScheme.secondaryContainer,
                            labelStyle: TextStyle(
                              color: theme.colorScheme.onSecondaryContainer,
                              fontWeight: FontWeight.w500,
                            ),
                            side: BorderSide.none,
                            visualDensity:
                                VisualDensity.compact, // Makes chip smaller
                            padding: const EdgeInsets.symmetric(horizontal: 8),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          // NEW WIDGET: This is the positioned time text that sits on top.
          Positioned(
            top: 12, // Aligns with the card's top padding
            right: 16, // Aligns with the card's right padding
            child: Text(
              _formatTime(log.date),
              style: theme.textTheme.bodySmall?.copyWith(
                // Using a semi-transparent variant color for secondary info
                color: theme.colorScheme.onSurfaceVariant.withAlpha(200),
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
