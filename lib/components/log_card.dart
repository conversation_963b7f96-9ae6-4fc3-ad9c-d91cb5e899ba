import 'package:flutter/material.dart';
import 'package:intl/intl.dart'; // Make sure you have the intl package in pubspec.yaml
import 'package:tut/models/log.dart'; // Adjust path if necessary

class LogCard extends StatelessWidget {
  final Logs log;

  const LogCard({super.key, required this.log});

  // Helper function to extract and format the time from the date string.
  // Handles multiple date formats including [6/8/2025 11:17:53 PM] format
  String _formatTime(String dateString) {
    try {
      DateTime dateTime;

      // Handle format like [6/8/2025 11:17:53 PM]
      if (dateString.startsWith('[') && dateString.endsWith(']')) {
        final cleanDate = dateString.substring(1, dateString.length - 1);
        dateTime = DateFormat('M/d/yyyy h:mm:ss a').parse(cleanDate);
      }
      // Handle ISO 8601 format as fallback
      else {
        dateTime = DateTime.parse(dateString);
      }

      return DateFormat.jm().format(dateTime);
    } catch (e) {
      // Try additional parsing attempts for various formats
      try {
        // Try without brackets
        final cleanDate = dateString.replaceAll(RegExp(r'[\[\]]'), '');
        final dateTime = DateFormat('M/d/yyyy h:mm:ss a').parse(cleanDate);
        return DateFormat.jm().format(dateTime);
      } catch (e2) {
        print("Error parsing date: $dateString, Error: $e");
        return '--:--';
      }
    }
  }

  // Helper function to get a color based on the operation type
  Color _getOperationColor(String operation) {
    switch (operation.toUpperCase()) {
      case 'CREATE':
        return Colors.green.shade100;
      case 'UPDATE':
        return Colors.blue.shade100;
      case 'DELETE':
        return Colors.red.shade100;
      case 'READ':
      default:
        return Colors.grey.shade200;
    }
  }

  // Helper function to get the text color for the operation chip
  Color _getOperationTextColor(String operation) {
    switch (operation.toUpperCase()) {
      case 'CREATE':
        return Colors.green.shade900;
      case 'UPDATE':
        return Colors.blue.shade900;
      case 'DELETE':
        return Colors.red.shade900;
      case 'READ':
      default:
        return Colors.grey.shade800;
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 4.0),
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(
          color: theme.colorScheme.outlineVariant.withValues(alpha: 0.5),
          width: 0.5,
        ),
      ),
      child: InkWell(
        borderRadius: BorderRadius.circular(12),
        onTap: () {
          // Future: Add tap functionality for log details
        },
        child: Padding(
          padding: const EdgeInsets.all(12.0),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Avatar with operation type indicator
              Stack(
                children: [
                  CircleAvatar(
                    radius: 18,
                    backgroundColor: theme.colorScheme.primaryContainer,
                    child: Text(
                      log.uid.substring(0, 1).toUpperCase(),
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.onPrimaryContainer,
                      ),
                    ),
                  ),
                  Positioned(
                    bottom: -2,
                    right: -2,
                    child: Container(
                      width: 12,
                      height: 12,
                      decoration: BoxDecoration(
                        color: _getOperationColor(log.operation),
                        shape: BoxShape.circle,
                        border: Border.all(
                          color: theme.colorScheme.surface,
                          width: 1.5,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(width: 12),

              // Main Content Column
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Header row with UID and time
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Expanded(
                          child: Text(
                            log.uid,
                            style: theme.textTheme.titleSmall?.copyWith(
                              fontWeight: FontWeight.w600,
                              height: 1.2,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        Text(
                          _formatTime(log.date),
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: theme.colorScheme.onSurfaceVariant,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 6),

                    // Operation details - given more space
                    Text(
                      log.operation,
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: theme.colorScheme.onSurface,
                        height: 1.3,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 8),

                    // Type and operation chips - more compact
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 2,
                          ),
                          decoration: BoxDecoration(
                            color: _getOperationColor(log.operation),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Text(
                            log.type,
                            style: TextStyle(
                              color: _getOperationTextColor(log.operation),
                              fontSize: 11,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                        const SizedBox(width: 8),
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 2,
                          ),
                          decoration: BoxDecoration(
                            color: theme.colorScheme.secondaryContainer,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Text(
                            _getOperationDisplayName(log.operation),
                            style: TextStyle(
                              color: theme.colorScheme.onSecondaryContainer,
                              fontSize: 11,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // Helper to get a shorter display name for operations
  String _getOperationDisplayName(String operation) {
    if (operation.length > 10) {
      return '${operation.substring(0, 8)}...';
    }
    return operation;
  }
}
